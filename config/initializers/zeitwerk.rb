# Configure Zeitwerk to ignore migration directories and other problematic directories
# This prevents Zeitwerk from trying to autoload migration files which have numeric names
# that are not valid Ruby constant names
Rails.autoloaders.main.ignore(
  Rails.root.join('db', 'migrate'),
  Rails.root.join('db'),
  Rails.root.join('tmp'),
  Rails.root.join('log'),
  Rails.root.join('vendor', 'assets'),
  Rails.root.join('public')
)

# Rails.autoloaders.main.ignore(
#   "app/assets",
#   "app/javascript",
#   "app/views",
# )

# Rails.autoloaders.main.collapse(
#   "app/controllers/concerns",
#   "app/models/concerns",
# )

# Rails.autoloaders.main.inflector.inflect(
#   "csv_importer" => "CSVImporter",
#   "html_parser" => "HTMLParser"
# )
