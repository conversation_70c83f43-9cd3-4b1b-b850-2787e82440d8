# Skip credentials loading for development
# This application uses APP_CONFIG instead of Rails credentials

# Override the credentials method to return a dummy object
Rails.application.define_singleton_method(:credentials) do
  OpenStruct.new(
    secret_key_base: ENV['SECRET_KEY_BASE'] || 'development_secret_key_base_' + ('a' * 64)
  )
end

# Also override the encrypted method to prevent access
Rails.application.define_singleton_method(:encrypted) do |*args|
  OpenStruct.new(
    secret_key_base: ENV['SECRET_KEY_BASE'] || 'development_secret_key_base_' + ('a' * 64)
  )
end
