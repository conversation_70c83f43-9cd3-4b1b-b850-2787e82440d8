#!/usr/bin/env ruby

# Script to fix :after options and duplicate indexes in migration files
Dir.glob("db/migrate/*.rb").each do |file|
  content = File.read(file)
  modified = false

  if content.include?(":after =>")
    puts "Fixing :after options in #{file}"
    # Remove :after options
    content = content.gsub(/, :after => :[a-zA-Z_]*/, '')
    modified = true
  end

  # Check for duplicate indexes created by belongs_to and add_index
  lines = content.split("\n")
  belongs_to_indexes = []
  add_index_lines = []

  lines.each_with_index do |line, index|
    if line.match(/t\.belongs_to :(\w+)/)
      belongs_to_indexes << "#{$1}_id"
    elsif line.match(/add_index.*:(\w+), :(\w+_id)/)
      add_index_lines << {index: index, table: $1, column: $2}
    end
  end

  # Remove duplicate add_index lines
  add_index_lines.each do |add_index|
    if belongs_to_indexes.include?(add_index[:column])
      puts "Removing duplicate index in #{file}: #{add_index[:column]}"
      lines[add_index[:index]] = ""
      modified = true
    end
  end

  if modified
    File.write(file, lines.join("\n"))
  end
end

puts "Done fixing migration issues"
