FROM ruby:3.2.2-bullseye

LABEL maintainer="Sharetribe Team <<EMAIL>>"

ENV REFRESHED_AT 2023-09-01

RUN apt-get update \
    && apt-get dist-upgrade -y \
    && apt-get install -y \
       build-essential \
       libpq-dev \
       nodejs \
       npm \
       netcat \
       nginx \
       imagemagick \
       libmagickwand-dev \
       libmagickcore-dev \
       git \
       curl \
       vim \
       postgresql-client \
       redis-tools

# Install bundler
RUN gem install bundler:2.4.10

# Run as non-privileged user
RUN useradd -m -s /bin/bash app \
	&& mkdir -p /opt/app /opt/app/client /opt/app/log /opt/app/tmp /usr/local/bundle \
	&& chown -R app:app /opt/app /usr/local/bundle

WORKDIR /opt/app

COPY --chown=app:app Gemfile /opt/app/

USER app

# For development, we don't use --deployment flag
RUN bundle config set --local path '/usr/local/bundle' \
    && bundle install --jobs 4 --retry 3

COPY --chown=app:app . /opt/app/

EXPOSE 3000

CMD ["script/startup.sh"]
