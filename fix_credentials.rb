#!/usr/bin/env ruby

# This script creates a proper Rails 7 credentials file
require 'openssl'
require 'base64'
require 'securerandom'

# Master key (must be 32 bytes)
master_key_hex = "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"
master_key = [master_key_hex].pack('H*')

# Generate a random secret key base
secret_key_base = SecureRandom.hex(64)

# Create credentials content
credentials_content = <<~YAML
# Used as the base secret for all MessageVerifiers in Rails, including the one protecting cookies.
secret_key_base: #{secret_key_base}
YAM<PERSON>

puts "Creating credentials with content:"
puts credentials_content

# Create cipher for AES-128-GCM
cipher = OpenSSL::Cipher.new('aes-128-gcm')
cipher.encrypt
cipher.key = master_key[0, 16]  # AES-128 needs 16 bytes

# Generate random IV
iv = cipher.random_iv

# Encrypt the content
encrypted_data = cipher.update(credentials_content) + cipher.final
auth_tag = cipher.auth_tag

# Create the final encrypted content in Rails format
# Rails uses: encrypted_data + auth_tag + iv, all base64 encoded
final_content = Base64.strict_encode64(encrypted_data + auth_tag + iv)

# Write the credentials file
File.write('config/credentials.yml.enc', final_content)

puts "Created config/credentials.yml.enc"
puts "Master key: #{master_key_hex}"
