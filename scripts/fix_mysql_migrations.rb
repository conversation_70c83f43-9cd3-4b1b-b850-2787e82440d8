#!/usr/bin/env ruby

# Script to batch-fix common MySQL to PostgreSQL migration issues
# Usage: ruby scripts/fix_mysql_migrations.rb

require 'fileutils'

class MigrationFixer
  def initialize
    @migration_dir = 'db/migrate'
    @backup_dir = 'db/migrate_backups'
    @fixes_applied = []
  end

  def run
    puts "Starting MySQL to PostgreSQL migration fixes..."
    
    # Create backup directory
    FileUtils.mkdir_p(@backup_dir) unless Dir.exist?(@backup_dir)
    
    # Get all migration files
    migration_files = Dir.glob("#{@migration_dir}/*.rb")
    
    puts "Found #{migration_files.length} migration files"
    
    migration_files.each do |file|
      fix_migration_file(file)
    end
    
    puts "\nSummary of fixes applied:"
    @fixes_applied.each do |fix|
      puts "  - #{fix}"
    end
    
    puts "\nBackups saved to: #{@backup_dir}"
    puts "Migration fixes completed!"
  end

  private

  def fix_migration_file(file)
    content = File.read(file)
    original_content = content.dup
    
    # Apply all fixes
    content = fix_after_option(content, file)
    content = fix_nil_option(content, file)
    content = fix_remove_multiple_columns(content, file)
    content = fix_mysql_update_joins(content, file)
    content = fix_mysql_quotes(content, file)
    content = fix_boolean_comparisons(content, file)
    content = fix_sanitize_method(content, file)
    content = fix_update_all_syntax(content, file)
    
    # Only write if changes were made
    if content != original_content
      # Create backup
      backup_file = "#{@backup_dir}/#{File.basename(file)}.backup"
      File.write(backup_file, original_content)
      
      # Write fixed content
      File.write(file, content)
      puts "Fixed: #{File.basename(file)}"
    end
  end

  def fix_after_option(content, file)
    if content.match(/:after\s*=>\s*:?\w+|after:\s*:?\w+/)
      @fixes_applied << "Removed :after option (#{File.basename(file)})"
      content.gsub!(/, :after\s*=>\s*:?\w+/, '')
      content.gsub!(/, after:\s*:?\w+/, '')
      content.gsub!(/:after\s*=>\s*:?\w+,\s*/, '')
      content.gsub!(/after:\s*:?\w+,\s*/, '')
    end
    content
  end

  def fix_nil_option(content, file)
    if content.match(/:nil\s*=>\s*(true|false)|nil:\s*(true|false)/)
      @fixes_applied << "Fixed :nil option to :null (#{File.basename(file)})"
      content.gsub!(/:nil\s*=>\s*/, ':null => ')
      content.gsub!(/nil:\s*/, 'null: ')
    end
    content
  end

  def fix_remove_multiple_columns(content, file)
    # Fix remove_column with multiple columns
    matches = content.scan(/remove_column\s+:\w+,\s*([^)]+)/)
    matches.each do |match|
      columns = match[0].split(',').map(&:strip)
      if columns.length > 1
        @fixes_applied << "Fixed remove_column with multiple columns (#{File.basename(file)})"
        table_match = content.match(/remove_column\s+(:\w+),/)
        if table_match
          table = table_match[1]
          new_removes = columns.map { |col| "    remove_column #{table}, #{col}" }.join("\n")
          old_line = content.match(/remove_column\s+:\w+,\s*[^)]+/)[0]
          content.gsub!(old_line, new_removes)
        end
      end
    end
    content
  end

  def fix_mysql_update_joins(content, file)
    # Fix MySQL UPDATE with INNER JOIN syntax
    if content.match(/UPDATE\s+\w+\s+\w+\s+INNER\s+JOIN/i)
      @fixes_applied << "Fixed MySQL UPDATE with INNER JOIN (#{File.basename(file)})"
      # This is complex and needs manual review, just flag it
      puts "  WARNING: #{File.basename(file)} contains MySQL UPDATE with INNER JOIN - needs manual review"
    end
    content
  end

  def fix_mysql_quotes(content, file)
    # Fix MySQL backticks to PostgreSQL double quotes
    if content.match(/`\w+`/)
      @fixes_applied << "Fixed MySQL backticks to PostgreSQL quotes (#{File.basename(file)})"
      content.gsub!(/`(\w+)`/, '"\1"')
    end
    content
  end

  def fix_boolean_comparisons(content, file)
    # Fix boolean comparisons (1/0 to true/false)
    if content.match(/=\s*[01]\s*(?:AND|OR|WHERE|\)|$)/i)
      @fixes_applied << "Fixed boolean comparisons (#{File.basename(file)})"
      content.gsub!(/=\s*1(?=\s*(?:AND|OR|WHERE|\)|$))/i, '= true')
      content.gsub!(/=\s*0(?=\s*(?:AND|OR|WHERE|\)|$))/i, '= false')
    end
    content
  end

  def fix_sanitize_method(content, file)
    if content.match(/ActiveRecord::Base\.sanitize\(/)
      @fixes_applied << "Fixed sanitize method to sanitize_sql (#{File.basename(file)})"
      content.gsub!(/ActiveRecord::Base\.sanitize\(/, 'ActiveRecord::Base.sanitize_sql(')
    end
    content
  end

  def fix_update_all_syntax(content, file)
    # Fix old update_all syntax with two arguments
    if content.match(/\.update_all\([^,]+,\s*[^)]+\)/)
      @fixes_applied << "Fixed update_all syntax (#{File.basename(file)})"
      puts "  WARNING: #{File.basename(file)} contains old update_all syntax - needs manual review"
    end
    content
  end
end

# Run the fixer
if __FILE__ == $0
  fixer = MigrationFixer.new
  fixer.run
end
