GIT
  remote: https://github.com/Charly3X/compass.git
  revision: 50a75b4903765cb7135f81ae2031298c55de538e
  specs:
    compass (1.0.3)
      chunky_png (~> 1.2)
      compass-core (~> 1.0.2)
      compass-import-once (~> 1.0.5)
      rb-fsevent (>= 0.9.3)
      rb-inotify (>= 0.9)
      sass (>= 3.3.13, < 3.5)
    compass-core (1.0.2)
      multi_json (~> 1.0)
      sass (>= 3.3.0, < 3.5)
    compass-import-once (1.0.5)
      sass (>= 3.2, < 3.5)

GIT
  remote: https://github.com/Charly3X/dynamic_form.git
  revision: 58c003983697ea244ae1072959505c373fbab6cf
  specs:
    dynamic_form (1.1.5)

GIT
  remote: https://github.com/Charly3X/transit-ruby.git
  revision: 7e947f999ba96e49c85db33d83c5202a8a8b9000
  specs:
    transit-ruby (0.9)
      addressable (~> 2.3)
      msgpack (~> 1.2.0)
      oj (~> 3.13)

GIT
  remote: https://github.com/ithouse/fake_stripe.git
  revision: 56fe73dc420d161ecf9842739af7d857031ca1b2
  ref: 56fe73dc420d161ecf9842739af7d857031ca1b2
  specs:
    fake_stripe (0.1.0)
      capybara
      sinatra
      webmock

GIT
  remote: https://github.com/ithouse/mercury.git
  revision: 1a9d4ac5a0a5fd0d459ff1986f9f05e617415b16
  ref: 1a9d4ac5a0a5fd0d459ff1986f9f05e617415b16
  branch: master
  specs:
    mercury-rails (0.9.0)
      coffee-rails (>= 3.2.2)
      railties (>= 3.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activerecord-session_store (2.0.0)
      actionpack (>= *******)
      activerecord (>= *******)
      multi_json (~> 1.11, >= 1.11.2)
      rack (>= 2.0.8, < 3)
      railties (>= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    airbrake (13.0.5)
      airbrake-ruby (~> 6.0)
    airbrake-ruby (6.2.2)
      rbtree3 (~> 0.6)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    ast (2.4.3)
    awesome_print (1.9.2)
    aws-eventstream (1.3.2)
    aws-partitions (1.1108.0)
    aws-sdk-core (3.224.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.101.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.114.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.4)
    aws-sdk-ses (1.47.0)
      aws-sdk-core (~> 3, >= 3.127.0)
      aws-sigv4 (~> 1.1)
    aws-sigv4 (1.11.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.0)
    biz (1.8.2)
      clavius (~> 1.0)
      tzinfo
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    builder (3.3.0)
    byebug (12.0.0)
    camertron-eprun (1.1.1)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    cgi (0.4.2)
    chargebee (2.54.0)
      cgi (>= 0.1.0, < 1.0.0)
    childprocess (5.1.0)
      logger (~> 1.5)
    chunky_png (1.4.0)
    clavius (1.0.4)
    cldr-plurals-runtime-rb (1.1.0)
    climate_control (0.2.0)
    cliver (0.3.2)
    cocoon (1.2.15)
    coderay (1.1.3)
    coffee-rails (5.0.0)
      coffee-script (>= 2.2.0)
      railties (>= 5.2.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    color (1.8)
    compass-rails (4.0.0)
      compass (~> 1.0.0)
      sass-rails (< 5.1)
      sprockets (< 4.0)
    concurrent-ruby (1.3.5)
    connection_pool (2.2.5)
    countries (7.1.1)
      unaccent (~> 0.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    css_parser (1.11.0)
      addressable
    csv (3.3.4)
    cucumber (9.2.1)
      builder (~> 3.2)
      cucumber-ci-environment (> 9, < 11)
      cucumber-core (> 13, < 14)
      cucumber-cucumber-expressions (~> 17.0)
      cucumber-gherkin (> 24, < 28)
      cucumber-html-formatter (> 20.3, < 22)
      cucumber-messages (> 19, < 25)
      diff-lcs (~> 1.5)
      mini_mime (~> 1.1)
      multi_test (~> 1.1)
      sys-uname (~> 1.2)
    cucumber-ci-environment (10.0.1)
    cucumber-core (13.0.3)
      cucumber-gherkin (>= 27, < 28)
      cucumber-messages (>= 20, < 23)
      cucumber-tag-expressions (> 5, < 7)
    cucumber-cucumber-expressions (17.1.0)
      bigdecimal
    cucumber-gherkin (27.0.0)
      cucumber-messages (>= 19.1.4, < 23)
    cucumber-html-formatter (21.9.0)
      cucumber-messages (> 19, < 28)
    cucumber-messages (22.0.0)
    cucumber-rails (3.1.1)
      capybara (>= 3.11, < 4)
      cucumber (>= 5, < 10)
      railties (>= 5.2, < 9)
    cucumber-tag-expressions (6.1.2)
    database_cleaner (2.1.0)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.2.1)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    delayed_job (4.1.13)
      activesupport (>= 3.0, < 9.0)
    delayed_job_active_record (4.1.11)
      activerecord (>= 3.0, < 9.0)
      delayed_job (>= 3.0, < 5)
    delayed_paperclip (3.0.1)
      activejob (>= 4.2)
      paperclip (>= 3.3)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-encryptable (0.2.0)
      devise (>= 2.1.0)
    diff-lcs (1.6.2)
    domain_name (0.6.20240107)
    drb (2.2.3)
    email_spec (2.3.0)
      htmlentities (~> 4.3.3)
      launchy (>= 2.1, < 4.0)
      mail (~> 2.7)
    erubi (1.13.1)
    execjs (2.10.0)
    ey-hmac (2.4.0)
    factory_bot (6.5.1)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faraday (1.10.4)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-encoding (0.0.6)
      faraday
    faraday-excon (1.1.0)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.1.0)
      multipart-post (~> 2.0)
    faraday-net_http (1.0.2)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    faraday_middleware (1.2.1)
      faraday (~> 1.0)
    fast-polylines (2.2.2)
    ffi (1.15.5)
    flying-sphinx (2.2.0)
      ey-hmac (~> 2.2)
      faraday (>= 0.7)
      gzipped_tar (~> 0.1.1)
      multi_json (>= 1.3.0)
      pusher-client (~> 0.3)
      thinking-sphinx (>= 4.0.0)
    formatador (1.1.0)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    guard (2.19.1)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      logger (~> 1.6)
      lumberjack (>= 1.0.12, < 2.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      ostruct (~> 0.6)
      pry (>= 0.13.0)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    guard-compat (1.2.1)
    guard-rspec (4.7.3)
      guard (~> 2.1)
      guard-compat (~> 1.1)
      rspec (>= 2.99.0, < 4.0)
    gzipped_tar (0.1.2)
    haml (6.2.5)
      temple (>= 0.8.2)
      thor
      tilt
    hashdiff (1.2.0)
    hashie (5.0.0)
    hiredis (0.6.3)
    htmlentities (4.3.4)
    http-accept (1.7.0)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    i18n-js (3.9.2)
      i18n (>= 0.6.6)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    innertube (1.1.0)
    intercom (4.1.3)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jmespath (1.6.2)
    joiner (0.6.0)
      activerecord (>= 6.1.0)
    jquery-rails (4.4.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    js-routes (2.2.10)
      railties (>= 4)
      sorbet-runtime
    json (2.12.2)
    jwt (2.10.1)
      base64
    kt-paperclip (7.2.2)
      activemodel (>= 4.2.0)
      activesupport (>= 4.2.0)
      marcel (~> 1.0.1)
      mime-types
      terrapin (>= 0.6.0, < 2.0)
    language_server-protocol (********)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    libv8-node (*********-x86_64-linux)
    lint_roller (1.1.0)
    listen (3.7.1)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    lograge (0.12.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lumberjack (1.2.10)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    mail_view (2.0.4)
      tilt
    marcel (1.0.4)
    matrix (0.4.2)
    memoist (0.16.2)
    method_source (1.1.0)
    middleware (0.1.0)
    mime-types (3.7.0)
      logger
      mime-types-data (~> 3.2025, >= 3.2025.0507)
    mime-types-data (3.2025.0527)
    mimemagic (0.3.10)
      nokogiri (~> 1)
      rake
    mini_magick (4.11.0)
    mini_mime (1.1.5)
    mini_racer (0.6.4)
      libv8-node (~> *********)
    minitest (5.25.5)
    monetize (1.13.0)
      money (~> 6.12)
    money (6.19.0)
      i18n (>= 0.6.4, <= 2)
    money-rails (1.15.0)
      activesupport (>= 3.0)
      monetize (~> 1.9)
      money (~> 6.13)
      railties (>= 3.0)
    msgpack (1.2.10)
    multi_json (1.15.0)
    multi_test (1.1.0)
    multi_xml (0.7.2)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mustermann (3.0.3)
      ruby2_keywords (~> 0.0.1)
    mysql2 (0.5.6)
    nenv (0.3.0)
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    netrc (0.11.0)
    newrelic_rpm (9.17.0)
    nio4r (2.7.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    notiffany (0.1.3)
      nenv (~> 0.1)
      shellany (~> 0.0)
    oauth2 (2.0.11)
      faraday (>= 0.17.3, < 4.0)
      jwt (>= 1.0, < 4.0)
      logger (~> 1.2)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0, >= 2.0.3)
      version_gem (>= 1.1.8, < 3)
    oj (3.16.11)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    omniauth (2.1.3)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-facebook (9.0.0)
      omniauth-oauth2 (~> 1.2)
    omniauth-google-oauth2 (1.1.3)
      jwt (>= 2.0)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-linkedin-openid (1.0.2)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    optimist (3.2.1)
    orm_adapter (0.5.0)
    ostruct (0.6.1)
    paperclip (6.1.0)
      activemodel (>= 4.2.0)
      activesupport (>= 4.2.0)
      mime-types
      mimemagic (~> 0.3.0)
      terrapin (~> 0.6.0)
    parallel (1.27.0)
    parser (*******)
      ast (~> 2.4.1)
      racc
    passenger (6.0.27)
      rack (>= 1.6.13)
      rackup (>= 1.0.1)
      rake (>= 12.3.3)
    paypal-sdk-core (0.3.6)
      multi_json (~> 1.0)
      xml-simple
    paypal-sdk-merchant (1.117.4)
      paypal-sdk-core (~> 0.3.0)
    paypal-sdk-permissions (1.96.6)
      paypal-sdk-core (~> 0.3.0)
    pg (1.4.6)
    poltergeist (1.18.1)
      capybara (>= 2.1, < 4)
      cliver (~> 0.3.1)
      websocket-driver (>= 0.2.0)
    possibly (1.0.1)
    pp (0.6.2)
      prettyprint
    premailer (1.16.0)
      addressable
      css_parser (>= 1.6.0)
      htmlentities (>= 4.0.0)
    premailer-rails (1.11.1)
      actionmailer (>= 3)
      premailer (~> 1.7, >= 1.7.9)
    prettyprint (0.2.0)
    prism (1.4.0)
    pry (0.15.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.11.0)
      byebug (~> 12.0)
      pry (>= 0.13, < 0.16)
    psych (3.3.4)
    public_suffix (6.0.2)
    puma (5.6.9)
      nio4r (~> 2.0)
    pusher-client (0.6.2)
      json
      websocket (~> 1.0)
    racc (1.8.1)
    rack (2.2.16)
    rack-attack (6.6.1)
      rack (>= 1.0, < 3)
    rack-mini-profiler (3.3.1)
      rack (>= 1.2.0)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (1.0.1)
      rack (< 3)
      webrick
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-i18n (7.0.10)
      i18n (>= 0.7, < 2)
      railties (>= 6.0.0, < 8)
    rails_12factor (0.0.3)
      rails_serve_static_assets
      rails_stdout_logging
    rails_serve_static_assets (0.0.5)
    rails_stdout_logging (0.0.5)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rbtree (0.4.6)
    rbtree3 (0.7.1)
    rdoc (*******)
    react_on_rails (13.0.2)
      addressable
      connection_pool
      execjs (~> 2.5)
      rails (>= 5.2)
      rainbow (~> 3.0)
    recaptcha (5.10.1)
      json
    redcarpet (3.6.1)
    redis (4.6.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    request_store (1.5.1)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.4.1)
    riddle (2.4.3)
    rspec (3.13.1)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.4)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.0.2)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.4)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rubocop (1.74.0)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.38.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= *******)
      prism (~> 1.4)
    rubocop-performance (1.24.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.72.1, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.30.3)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.72.1, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.3)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    sass (3.4.24)
    sass-rails (5.0.8)
      railties (>= 5.2.0)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    securerandom (0.4.1)
    select2-rails (4.0.13)
    selenium-webdriver (4.29.1)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    set (1.1.2)
    shellany (0.0.1)
    simpleidn (0.2.3)
    sinatra (3.2.0)
      mustermann (~> 3.0)
      rack (~> 2.2, >= 2.2.4)
      rack-protection (= 3.2.0)
      tilt (~> 2.0)
    sitemap_generator (6.2.1)
      builder (~> 3.0)
    snaky_hash (2.0.3)
      hashie (>= 0.1.0, < 6)
      version_gem (>= 1.1.8, < 3)
    sorbet-runtime (0.5.12135)
    sorted_set (1.0.3)
      rbtree
      set (~> 1.0)
    sprockets (3.7.5)
      base64
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    statesman (12.1.0)
    stringex (2.8.6)
    stripe (5.55.0)
    sys-uname (1.3.1)
      ffi (~> 1.1)
    temple (0.10.3)
    terrapin (0.6.0)
      climate_control (>= 0.0.3, < 1.0)
    thinking-sphinx (5.6.0)
      activerecord (>= 4.2.0)
      builder (>= 2.1.2)
      innertube (>= 1.0.2)
      joiner (>= 0.3.4)
      middleware (>= 0.1.0)
      riddle (~> 2.3)
    thor (1.3.2)
    tilt (2.6.0)
    timecop (0.9.10)
    timeout (0.4.3)
    truncate_html (0.9.3)
    ts-delayed-delta (2.1.0)
      activerecord (>= 2.0)
      delayed_job
      thinking-sphinx (>= 1.5.0)
    twilio-ruby (5.77.0)
      faraday (>= 0.9, < 3.0)
      jwt (>= 1.5, < 3.0)
      nokogiri (>= 1.6, < 2.0)
    twitter_cldr (6.11.5)
      camertron-eprun
      cldr-plurals-runtime-rb (~> 1.1)
      tzinfo
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    tzinfo-data (1.2022.7)
      tzinfo (>= 1.0.0)
    uglifier (4.2.1)
      execjs (>= 0.3.0, < 3)
    unaccent (0.4.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    useragent (0.16.11)
    uuidtools (2.2.0)
    version_gem (1.1.8)
    warden (1.2.9)
      rack (>= 2.0.9)
    web_translate_it (2.6.4)
      multi_json
      multipart-post (~> 2.0)
      optimist (~> 3.0)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket (1.2.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    will_paginate (3.3.1)
    xml-simple (1.1.9)
      rexml
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.3)
    zeus (0.17.0)
      method_source (>= 0.6.7)

PLATFORMS
  x86_64-linux

DEPENDENCIES
  activerecord-session_store (~> 2.0.0)
  airbrake (~> 13.0.5)
  annotate (~> 3.2.0)
  awesome_print (~> 1.9.2)
  aws-sdk-s3 (~> 1.114.0)
  aws-sdk-ses (~> 1.47.0)
  bcrypt (~> 3.1.17)
  biz (~> 1.8.2)
  bootsnap (~> 1.18.4)
  capybara (~> 3.40.0)
  chargebee (~> 2.0)
  cocoon (~> 1.2.15)
  coffee-rails (~> 5.0.0)
  color (~> 1.8)
  compass!
  compass-rails (~> 4.0.0)
  connection_pool (~> 2.2.5)
  countries (~> 7.1.1)
  css_parser (~> 1.11.0)
  cucumber (= 9.2.1)
  cucumber-rails (~> 3.1.1)
  database_cleaner (~> 2.1.0)
  delayed_job (~> 4.1.3)
  delayed_job_active_record (~> 4.1.3)
  delayed_paperclip (~> 3.0.1)
  devise (~> 4.9.4)
  devise-encryptable (~> 0.2.0)
  dynamic_form (~> 1.1.5)!
  email_spec (~> 2.3.0)
  factory_bot_rails
  fake_stripe!
  faraday (~> 1.10.4)
  faraday-encoding (~> 0.0.5)
  faraday_middleware (~> 1.2.0)
  fast-polylines (~> 2.2.2)
  ffi (~> 1.15.5)
  flying-sphinx (~> 2.2.0)
  geocoder (~> 1.8)
  guard-rspec
  haml (~> 6.2.3)
  hiredis (~> 0.6.3)
  i18n-js (~> 3.9)
  image_processing (~> 1.14.0)
  intercom (~> 4.1.3)
  jquery-rails (~> 4.4.0)
  js-routes (~> 2.2.3)
  kt-paperclip (~> 7.2.2)
  launchy (~> 3.1.1)
  listen (~> 3.7.1)
  lograge (~> 0.12.0)
  mail (~> 2.8)
  mail_view (~> 2.0.4)
  memoist (~> 0.16.2)
  mercury-rails!
  mini_magick (~> 4.11.0)
  mini_racer (~> 0.6.0)
  money-rails (~> 1.15.0)
  multi_test (= 1.1.0)
  mysql2 (~> 0.5.6)
  newrelic_rpm (~> 9.17.0)
  oj (~> 3.16.10)
  omniauth-facebook (~> 9.0.0)
  omniauth-google-oauth2 (~> 1.1.1)
  omniauth-linkedin-openid
  omniauth-rails_csrf_protection (~> 1.0.1)
  passenger (~> 6.0.27)
  paypal-sdk-merchant (~> 1.117.2)
  paypal-sdk-permissions (~> 1.96.4)
  pg (~> 1.4.0)
  poltergeist (~> 1.18.1)
  possibly (~> 1.0.1)
  premailer-rails (~> 1.11.1)
  pry (~> 0.14)
  pry-byebug (~> 3.10)
  psych (< 4)
  public_suffix
  puma (~> 5.6.9)
  rack-attack (~> 6.6.1)
  rack-mini-profiler (~> 3.0)
  rails (= *******)
  rails-controller-testing (~> 1.0.5)
  rails-i18n (~> 7.0)
  rails_12factor (~> 0.0.3)
  rb-fsevent
  rb-inotify (~> 0.10)
  react_on_rails (= 13.0.2)
  recaptcha (~> 5.10.0)
  redcarpet (~> 3.6.0)
  redis (~> 4.6.0)
  request_store (~> 1.5.1)
  rest-client (~> 2.1.0)
  rspec-rails (~> 7.0.0)
  rspec_junit_formatter (~> 0.6.0)
  rubocop (~> 1.74.0)
  rubocop-performance (~> 1.24.0)
  rubocop-rails (~> 2.30.3)
  rubyzip (~> 2.3.2)
  sass (= 3.4.24)
  sass-rails (~> 5.0.6)
  select2-rails (~> 4.0.13)
  selenium-webdriver (~> 4.29.1)
  simpleidn (~> 0.2.1)
  sitemap_generator (~> 6.2.1)
  sorted_set
  statesman (~> 12.1.0)
  stringex (~> 2.8.5)
  stripe (~> 5.55.0)
  thinking-sphinx (~> 5.5)
  timecop (~> 0.9.10)
  transit-ruby (= 0.9)!
  truncate_html (~> 0.9.3)
  ts-delayed-delta (= 2.1.0)
  twilio-ruby (~> 5.75)
  twitter_cldr (~> 6.11.3)
  tzinfo-data (~> 1.2022.1)
  uglifier (~> 4.2.0)
  uuidtools (~> 2.2.0)
  web_translate_it (~> 2.6.2)
  will_paginate (~> 3.3.1)
  zeus

RUBY VERSION
   ruby 3.2.2p53

BUNDLED WITH
   2.4.10
