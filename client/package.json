{"repository": "sharetribe/sharetribe", "license": "MIT", "engines": {"node": "18.16.0", "npm": "9.5.1"}, "scripts": {"ensure-node-version": "check-node-version --package", "lint": "npm run eslint && npm run stylelint", "eslint": "npm run ensure-node-version && eslint .", "stylelint": "npm run ensure-node-version && stylelint **/*.css", "build:client": "npm run ensure-node-version && webpack --config webpack.client.config.js", "build:server": "npm run ensure-node-version && webpack --config webpack.server.config.js", "build:production:client": "NODE_ENV=production npm run ensure-node-version && webpack --config webpack.client.config.js", "build:production:server": "NODE_ENV=production npm run ensure-node-version && webpack --config webpack.server.config.js", "build:production": "npm run ensure-node-version && npm run build:production:client && npm run build:production:server", "build:dev:client": "npm run ensure-node-version && webpack -w --config webpack.client.config.js", "build:dev:server": "npm run ensure-node-version && webpack -w --config webpack.server.config.js", "build:test": "npm run ensure-node-version && npm run build:client && npm run build:server", "print-phantomjs-version": "npm run ensure-node-version && phantomjs --version", "start-phantomjs": "npm run ensure-node-version && phantomjs --webdriver=8910", "styleguide": "npm run ensure-node-version && start-storybook -p 9001", "deploy-storybook": "npm run ensure-node-version && storybook-to-ghpages", "test:stories": "npm run ensure-node-version && mocha './app/components/**/*.story.js' --reporter mocha-circleci-reporter --require babel-register --require ignore-styles --require ./mochaConfig.js", "test:specs": "npm run ensure-node-version && mocha './app/specs/*.spec.js' --reporter mocha-circleci-reporter --require babel-register --require ignore-styles --require ./mochaConfig.js", "test": "npm run ensure-node-version && npm run test:stories && npm run test:specs", "test:devspecs": "mocha './app/specs/*.spec.js' --require babel-register"}, "dependencies": {"babel-cli": "6.18.0", "babel-core": "6.18.2", "babel-loader": "6.2.8", "babel-polyfill": "6.16.0", "babel-preset-es2015": "6.18.0", "babel-preset-react": "6.16.0", "babel-preset-stage-2": "6.18.0", "babel-runtime": "6.18.0", "check-node-version": "1.1.2", "classnames": "2.2.5", "css-loader": "0.26.0", "customfile-loader": "./customloaders/customfileloader", "es6-shim": "0.35.1", "extract-text-webpack-plugin": "1.0.1", "file-loader": "0.9.0", "i18n-js": "http://github.com/sharetribe/i18n-js/archive/interpolation-mode.tar.gz", "immutable": "3.8.1", "imports-loader": "0.6.5", "json-loader": "https://registry.npmjs.org/json-loader/-/json-loader-0.5.4.tgz", "loader-utils": "0.2.16", "lodash": "4.17.2", "moment": "2.17.0", "node-libs-browser": "2.0.0", "node-sass": "4.14.1", "numbro": "1.9.3", "postcss-cssnext": "2.8.0", "postcss-loader": "0.13.0", "postcss-mixins": "5.4.0", "r-dom": "2.3.1", "raw-loader": "0.5.1", "react": "16.1.1", "react-addons-css-transition-group": "16.0.0-alpha.3", "react-addons-transition-group": "16.0.0-alpha.3", "react-addons-shallow-compare": "16.0.0-alpha.3", "react-dates": "4.1.0", "react-dom": "16.1.2", "react-on-rails": "13.0.2", "react-redux": "4.4.6", "redux": "3.6.0", "redux-thunk": "2.1.0", "resolve-url-loader": "1.6.0", "sass-loader": "4.0.2", "style-loader": "0.13.1", "transit-js": "0.8.846", "webpack": "1.13.3", "whatwg-fetch": "2.0.1"}, "devDependencies": {"@kadira/storybook": "2.32.1", "@kadira/storybook-deployer": "github:mporkola/storybook-deployer", "babel-eslint": "7.0.0", "babel-plugin-react-transform": "2.0.2", "babel-register": "6.18.0", "chai": "3.5.0", "chai-enzyme": "0.6.1", "enzyme": "2.6.0", "eslint": "3.8.1", "eslint-plugin-babel": "3.3.0", "eslint-plugin-react": "6.4.1", "ignore-styles": "5.0.1", "jsdom": "9.8.3", "json": "9.0.4", "mocha": "3.1.2", "mocha-circleci-reporter": "0.0.2", "phantomjs-prebuilt": "2.1.13", "react-addons-test-utils": "15.4.1", "react-transform-hmr": "1.0.4", "storybook-addon-specifications": "1.0.15", "stylelint": "7.5.0", "stylelint-config-standard": "13.0.2", "webpack-dev-server": "1.16.2"}, "browser": {"fs": false}}