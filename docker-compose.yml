services:
  db:
    image: postgres:14
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: sharetribe_development
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  sphinx:
    image: macbre/sphinxsearch:3.4.1
    restart: always
    ports:
      - "9306:9306"
      - "9312:9312"
    volumes:
      - ./sphinx.conf:/opt/sphinx/conf/sphinx.conf
      - sphinx_data:/var/lib/sphinxsearch/data
    depends_on:
      db:
        condition: service_healthy

  web:
    build: .
    ports:
      - "3000:3000"
    env_file: .env.development
    environment:
      - RAILS_ENV=development
      - DATABASE_URL=******************************************************
      - SPHINX_HOST=sphinx
      - WEBPACKER_DEV_SERVER_HOST=0.0.0.0
    depends_on:
      db:
        condition: service_healthy
      sphinx:
        condition: service_started
      memcached:
        condition: service_started
    volumes:
      - .:/opt/app
      - bundle_cache:/usr/local/bundle
    entrypoint: /opt/app/docker-entrypoint.sh
    command: bash -c "cd /opt/app && bundle exec rails server -b 0.0.0.0"

  worker:
    build: .
    env_file: .env.development
    environment:
      - RAILS_ENV=development
      - DATABASE_URL=******************************************************
      - SPHINX_HOST=sphinx
      - QUEUES=default,paperclip,mailers
      - MAGICK_MAP_LIMIT=64MiB
      - MAGICK_MEMORY_LIMIT=256MiB
      - MAGICK_TIME_LIMIT=30
    depends_on:
      db:
        condition: service_healthy
      sphinx:
        condition: service_started
      memcached:
        condition: service_started
      web:
        condition: service_started
    volumes:
      - .:/opt/app
      - bundle_cache:/usr/local/bundle
    entrypoint: /opt/app/docker-entrypoint.sh
    command: bash -c "cd /opt/app && bundle exec rake jobs:work"

  memcached:
    image: memcached:latest
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "11211"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  sphinx_data:
  bundle_cache:
  redis_data:
