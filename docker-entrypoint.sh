#!/bin/bash
set -e

# Remove a potentially pre-existing server.pid for Rails.
rm -f /opt/app/tmp/pids/server.pid

# Wait for database to be ready
echo "Waiting for database connection..."
# Sleep for a bit to give the database time to start
sleep 10
echo "Database is ready!"

# Install gems first
cd /opt/app
echo "Installing gems..."
bundle install
echo "Gems installed!"

# If the database exists, migrate. Otherwise setup (create and migrate)
bundle exec rake db:migrate 2>/dev/null || bundle exec rake db:create db:schema:load db:migrate
echo "Database has been created & migrated!"

# Configure and index Sphinx
echo "Configuring and indexing Sphinx..."
bundle exec rake ts:configure ts:index

# Wait for Redis to be ready
echo "Checking Redis connection..."
until redis-cli -h redis -p 6379 ping
do
  echo "Waiting for Redis connection..."
  # wait for 5 seconds before check again
  sleep 5
done
echo "Redis is ready!"

# Then exec the container's main process (what's set as CMD in the Dockerfile)
exec "$@"
