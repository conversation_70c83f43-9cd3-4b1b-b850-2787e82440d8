#!/bin/bash
set -e

# Remove a potentially pre-existing server.pid for Rails.
rm -f /opt/app/tmp/pids/server.pid

# Wait for database to be ready
echo "Waiting for database connection..."
# Sleep for a bit to give the database time to start
sleep 10

# Wait for PostgreSQL to be ready
until pg_isready -h db -p 5432 -U postgres; do
  echo "Waiting for PostgreSQL to be ready..."
  sleep 2
done
echo "Database is ready!"

# Install gems first
cd /opt/app
echo "Installing gems..."
bundle install
echo "Gems installed!"

# Check if database exists and handle setup accordingly
echo "Setting up database..."
if bundle exec rails runner "ActiveRecord::Base.connection" 2>/dev/null; then
  if [ "$SKIP_MIGRATIONS" = "true" ]; then
    echo "Skipping migrations due to SKIP_MIGRATIONS=true"
  else
    echo "Database connection successful, running migrations..."
    bundle exec rake db:migrate
  fi
else
  echo "Database connection failed, creating database and running setup..."
  bundle exec rake db:create db:schema:load db:migrate
fi
echo "Database setup complete!"

# Configure and index Sphinx
if [ "$SKIP_SPHINX" = "true" ]; then
  echo "Skipping Sphinx due to SKIP_SPHINX=true"
else
  echo "Configuring and indexing Sphinx..."
  bundle exec rake ts:configure ts:index
fi

# Wait for Redis to be ready
echo "Checking Redis connection..."
until redis-cli -h redis -p 6379 ping
do
  echo "Waiting for Redis connection..."
  # wait for 5 seconds before check again
  sleep 5
done
echo "Redis is ready!"

# Then exec the container's main process (what's set as CMD in the Dockerfile)
exec "$@"
