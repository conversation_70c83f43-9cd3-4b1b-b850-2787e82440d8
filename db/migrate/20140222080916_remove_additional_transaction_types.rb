class RemoveAdditionalTransactionTypes < ActiveRecord::Migration[7.2]
  def up
    # Skip data migration for fresh database setup
    # This migration was for removing additional transaction types
    # Since we're starting fresh, there are no existing transaction types to remove
    puts "Skipping additional transaction types removal for fresh database setup"
  end

  def down
    # No-op since we didn't do anything in up
    puts "No rollback needed for skipped migration"
  end
end
