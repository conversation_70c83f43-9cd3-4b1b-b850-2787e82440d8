class RemoveCheckoutPostpayTransactionProcesses < ActiveRecord::Migration[7.2]
  def up
    execute(<<-SQL)
      DELETE FROM transaction_processes
      WHERE id IN (
        SELECT transaction_processes.id
        FROM transaction_processes
        LEFT JOIN payment_gateways pg ON (pg.community_id = transaction_processes.community_id)
        WHERE pg.type = 'Checkout' AND transaction_processes.process = 'postpay'
      )
    SQL
  end
end
