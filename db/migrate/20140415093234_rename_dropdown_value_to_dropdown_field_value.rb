class RenameDropdownValueToDropdownFieldValue < ActiveRecord::Migration[7.2]
  def up
    # Update all doesn't play nicely with default scope
    # Using unscoped to bypass any default scopes
    CustomFieldValue.unscoped.where(type: 'DropdownValue').update_all(type: 'DropdownFieldValue')
  end

  def down
    CustomFieldValue.unscoped.where(type: 'DropdownFieldValue').update_all(type: 'DropdownValue')
  end
end
