class ChangeItemIdToIntegerInGroupsItems < ActiveRecord::Migration[7.2]
  def self.up
        remove_column :groups_items, :item_id
    add_column :groups_items
    remove_column :groups_items, :item_id
    remove_column :groups_items, :integer
  end

  def self.down
    remove_column :groups_items
    remove_column :groups_items, :item_id
    add_column :groups_items
    remove_column :groups_items, :item_id
    remove_column :groups_items, :string
  end
end