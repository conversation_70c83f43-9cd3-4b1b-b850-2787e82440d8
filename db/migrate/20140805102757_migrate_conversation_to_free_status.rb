class MigrateConversationToFreeStatus < ActiveRecord::Migration[7.2]
  def up
    # Skip data migration for fresh database setup
    # This migration was for migrating conversations to free status
    # Since we're starting fresh, there are no existing conversations to migrate
    puts "Skipping conversation to free status migration for fresh database setup"
  end

  def down
    # No-op since we didn't do anything in up
    puts "No rollback needed for skipped migration"
  end
end
