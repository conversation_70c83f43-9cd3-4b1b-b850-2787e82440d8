class FixTransactionProcessValues < ActiveRecord::Migration[7.2]
  def up
    execute("
      INSERT INTO transaction_processes (process, author_is_seller, community_id, created_at, updated_at)
      (
        SELECT
          'none',
          true,
          transaction_types.community_id,
          MIN(transaction_types.created_at) as created_at,
          MAX(transaction_types.updated_at) as updated_at
        FROM transaction_types

        LEFT JOIN transaction_processes ON (
          transaction_processes.community_id = transaction_types.community_id AND
          transaction_processes.process = 'none' AND
          transaction_processes.author_is_seller = true)

        WHERE
         transaction_types.type != 'Request' AND
         (transaction_types.price_field IS NULL OR transaction_types.type = 'Inquiry') AND
         transaction_processes.id IS NULL

        GROUP BY transaction_types.community_id, process, author_is_seller
      )
    ")

    execute(<<-SQL)
      UPDATE transaction_types
      SET transaction_process_id = transaction_processes.id
      FROM transaction_processes
      WHERE transaction_types.community_id = transaction_processes.community_id
        AND transaction_processes.process = 'none'
        AND transaction_processes.author_is_seller = true
        AND (transaction_types.type = 'Inquiry' OR transaction_types.price_field IS NULL)
    SQL

  end

  def down
    # noop
  end
end
