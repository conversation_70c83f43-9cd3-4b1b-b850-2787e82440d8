class MigrateListingIdsToCustomFieldOptionSelections < ActiveRecord::Migration[7.2]

  # Run this population script after all the code changes are deployed first
  def up
    execute(<<-SQL)
      UPDATE custom_field_option_selections
      SET listing_id = custom_field_values.listing_id
      FROM custom_field_values
      WHERE custom_field_values.id = custom_field_option_selections.custom_field_value_id
    SQL
  end

end
