class ChangeDateOfBirthTypeInBraintreeAccounts < ActiveRecord::Migration[7.2]
  def up
    # Use raw SQL to handle the type conversion with USING clause
    execute "ALTER TABLE braintree_accounts ALTER COLUMN date_of_birth TYPE date USING CASE WHEN date_of_birth = '' OR date_of_birth IS NULL THEN NULL ELSE date_of_birth::date END"
  end

  def down
    change_column :braintree_accounts, :date_of_birth, :string
  end
end
