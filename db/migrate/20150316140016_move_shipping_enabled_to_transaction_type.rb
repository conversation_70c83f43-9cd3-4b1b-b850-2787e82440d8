class MoveShippingEnabledToTransactionType < ActiveRecord::Migration[7.2]
  def up
    execute(<<-SQL)
      UPDATE transaction_types
      SET shipping_enabled = marketplace_settings.shipping_enabled
      FROM marketplace_settings
      WHERE transaction_types.community_id = marketplace_settings.community_id
        AND transaction_types.preauthorize_payment = true
    SQL
  end

  def down
    execute("UPDATE transaction_types SET shipping_enabled = false")
  end
end
