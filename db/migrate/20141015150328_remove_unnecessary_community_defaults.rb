class RemoveUnnecessaryCommunityDefaults < ActiveRecord::Migration[7.2]
  def up
        remove_column :communities, :category_change_allowed
    remove_column :communities
    remove_column :communities, :custom_fields_allowed
    remove_column :communities
    remove_column :communities, :privacy_policy_change_allowed
    remove_column :communities
    remove_column :communities, :terms_change_allowed
    change_column :communities
    remove_column :communities, :feedback_to_admin
    remove_column :communities, :boolean
    remove_column :communities, default: true
  end

  def down
    add_column :communities
    remove_column :communities, :category_change_allowed
    remove_column :communities, :boolean
    remove_column :communities, default: false
    add_column :communities
    remove_column :communities, :custom_fields_allowed
    remove_column :communities, :boolean
    remove_column :communities, default: false
    add_column :communities
    remove_column :communities, :privacy_policy_change_allowed
    remove_column :communities, :boolean
    remove_column :communities, default: false
    add_column :communities
    remove_column :communities, :terms_change_allowed
    remove_column :communities, :boolean
    remove_column :communities, default: false
    change_column :communities
    remove_column :communities, :feedback_to_admin
    remove_column :communities, :boolean
    remove_column :communities, default: false
  end
end