class FixRequestTransactionProcess < ActiveRecord::Migration[7.2]
  def up
    execute(<<-SQL)
      UPDATE transaction_types
      SET transaction_process_id = transaction_processes.id
      FROM transaction_processes
      WHERE transaction_types.community_id = transaction_processes.community_id
        AND transaction_processes.process = 'none'
        AND transaction_processes.author_is_seller = false
        AND transaction_types.type = 'Request'
    SQL
  end

  def down
    #noop
  end
end
