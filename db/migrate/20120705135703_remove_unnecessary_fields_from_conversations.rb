class RemoveUnnecessaryFieldsFromConversations < ActiveRecord::Migration[7.2]
  def self.up
            remove_column :conversations, :reserver_name
    remove_column :conversations
    remove_column :conversations
    remove_column :conversations, :pick_up_time
    remove_column :conversations
    remove_column :conversations
    remove_column :conversations, :return_time
    remove_column :conversations
    remove_column :conversations
    remove_column :conversations, :hidden_from_owner
    remove_column :conversations
    remove_column :conversations
    remove_column :conversations, :hidden_from_reserver
    remove_column :conversations
    remove_column :conversations
    remove_column :conversations, :favor_id
  end

  def self.down
    add_column :conversations
    remove_column :conversations
    remove_column :conversations, :reserver_name
    remove_column :conversations
    remove_column :conversations, :string
    add_column :conversations
    remove_column :conversations
    remove_column :conversations, :pick_up_time
    remove_column :conversations
    remove_column :conversations, :date
    add_column :conversations
    remove_column :conversations
    remove_column :conversations, :return_time
    remove_column :conversations
    remove_column :conversations, :date
    add_column :conversations
    remove_column :conversations
    remove_column :conversations, :hidden_from_owner
    remove_column :conversations
    remove_column :conversations, :integer
    remove_column :conversations
    remove_column :conversations, :default => 0
    add_column :conversations
    remove_column :conversations
    remove_column :conversations, :hidden_from_reserver
    remove_column :conversations
    remove_column :conversations, :integer
    remove_column :conversations
    remove_column :conversations, :default => 0
    add_column :conversations
    remove_column :conversations
    remove_column :conversations, :favor_id
    remove_column :conversations
    remove_column :conversations, :integer
  end
end