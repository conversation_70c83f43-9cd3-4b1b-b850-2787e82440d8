class RemoveReceiverIdAndListingIdFromMessage < ActiveRecord::Migration[7.2]
  def self.up
        remove_column :messages, :receiver_id
    remove_column :messages
    remove_column :messages, :listing_id
  end

  def self.down
    add_column :messages
    remove_column :messages, :receiver_id
    remove_column :messages, :string
    add_column :messages
    remove_column :messages, :listing_id
    remove_column :messages, :integer
  end
end