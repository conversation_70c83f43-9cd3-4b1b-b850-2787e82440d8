class CreateInitialPaymentGateways < ActiveRecord::Migration[7.2]
  def up
    # Skip data migration for fresh database setup
    # This migration was for creating initial payment gateway records
    # Since we're starting fresh, we can skip this data migration
    puts "Skipping initial payment gateways creation for fresh database setup"
  end

  def down
    # No-op since we didn't create anything in up
    puts "No rollback needed for skipped migration"
  end
end
