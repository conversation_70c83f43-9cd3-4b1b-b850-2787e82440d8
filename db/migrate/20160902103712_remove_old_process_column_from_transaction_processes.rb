class RemoveOldProcessColumnFromTransactionProcesses < ActiveRecord::Migration[7.2]
  def up
    # Remove old_process column if it exists
    remove_column :transaction_processes, :old_process if column_exists?(:transaction_processes, :old_process)
  end

  def down
    # Add back the column if needed for rollback
    add_column :transaction_processes, :old_process, :string, limit: 32 unless column_exists?(:transaction_processes, :old_process)
  end
end