class PopulatePaymentProcess < ActiveRecord::Migration[7.2]
  def up
    execute(<<-SQL)
      UPDATE transactions
      SET payment_process =
      CASE
      WHEN (communities.paypal_enabled = true OR payment_gateways.id IS NOT NULL) THEN
        CASE
        WHEN transaction_types.price_field = false THEN
          'none'
        WHEN transaction_types.preauthorize_payment THEN
          'preauthorize'
        ELSE
          'postpay'
        end
      ELSE
        'none'
      END
      FROM listings, transaction_types, payment_gateways, communities
      WHERE transaction_types.id = listings.transaction_type_id
        AND listings.id = transactions.listing_id
        AND communities.id = transactions.community_id
        AND communities.id = payment_gateways.community_id
    SQL
  end

  def down
    execute("UPDATE transactions SET payment_process = 'none'")
  end
end
