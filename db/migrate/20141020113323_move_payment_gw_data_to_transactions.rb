class MovePaymentGwDataToTransactions < ActiveRecord::Migration[7.2]
  def up
    execute(<<-EOQ)
       UPDATE transactions
       SET payment_gateway = (
         SELECT CASE
           WHEN transactions.current_state = 'free' THEN 'none'
           WHEN pp.id IS NOT NULL THEN 'paypal'
           WHEN pgw.type = 'Checkout' THEN 'checkout'
           WHEN pgw.type = 'BraintreePaymentGateway' THEN 'braintree'
           ELSE 'none'
         END
         FROM payment_gateways pgw
         LEFT JOIN paypal_payments pp ON (transactions.id = pp.transaction_id)
         WHERE pgw.community_id = transactions.community_id
         LIMIT 1
       )
    EOQ
  end

  def down
    execute(<<-EOQ)
      UPDATE transactions
      SET payment_gateway = 'none'
    EOQ
  end
end
