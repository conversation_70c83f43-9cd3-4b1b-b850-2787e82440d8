class RemoveHideRealNameFunctionality < ActiveRecord::Migration[7.2]
  def up
            remove_column :communities, :select_whether_name_is_shown_to_everybody
    remove_column :people
    remove_column :communities
    remove_column :communities, :show_real_name_to_other_users
  end

  def down
    add_column :communities
    remove_column :communities
    remove_column :communities, :select_whether_name_is_shown_to_everybody
    remove_column :communities
    remove_column :communities, :boolean
    remove_column :communities
    remove_column :communities, :default => 0
    add_column :people
    remove_column :communities
    remove_column :communities, :show_real_name_to_other_users
    remove_column :communities
    remove_column :communities, :boolean
    remove_column :communities
    remove_column :communities, :default => 1
  end
end