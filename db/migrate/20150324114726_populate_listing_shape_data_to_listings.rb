class PopulateListingShapeDataToListings < ActiveRecord::Migration[7.2]
  def up
    execute(<<-SQL)
      UPDATE listings
      SET
        transaction_process_id = transaction_types.transaction_process_id,
        shape_name_tr_key = transaction_types.name_tr_key,
        action_button_tr_key = transaction_types.action_button_tr_key
      FROM transaction_types
      WHERE listings.transaction_type_id = transaction_types.id
    SQL
  end

  def down
    execute(<<-SQL)
      UPDATE listings
      SET
        transaction_process_id = NULL,
        shape_name_tr_key = NULL,
        action_button_tr_key = NULL
    SQL
  end
end
