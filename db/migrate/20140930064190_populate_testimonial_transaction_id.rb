class PopulateTestimonialTransactionId < ActiveRecord::Migration[7.2]
  def up
    execute('
      UPDATE testimonials
      SET transaction_id = transactions.id
      FROM participations, conversations, transactions
      WHERE testimonials.participation_id = participations.id
        AND participations.conversation_id = conversations.id
        AND transactions.conversation_id = conversations.id
    ')
  end

  def down
    execute('
      UPDATE testimonials
      SET participation_id = participations.id
      FROM transactions, conversations, participations
      WHERE testimonials.transaction_id = transactions.id
        AND conversations.id = transactions.conversation_id
        AND participations.conversation_id = conversations.id
        AND participations.person_id = testimonials.author_id
    ')

    execute("UPDATE testimonials SET transaction_id = NULL WHERE transaction_id IS NOT NULL")
  end
end
