class PopulateExclusiveEndWhereUnitTypeIsNull < ActiveRecord::Migration[7.2]
  def up
    name = "Populate end_on_exclusive for bookings where unit type is NULL"
    exec_update([
                  "UPDATE bookings",
                  "SET end_on_exclusive = bookings.end_on + INTERVAL '1 day'",
                  "FROM transactions",
                  "WHERE transactions.id = bookings.transaction_id",
                  "AND transactions.unit_type IS NULL"
                ].join(" "), name, [])
  end
end
