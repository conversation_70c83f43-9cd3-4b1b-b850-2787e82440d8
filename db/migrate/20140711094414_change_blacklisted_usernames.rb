class ChangeBlacklistedUsernames < ActiveRecord::Migration[7.2]
  def up
    # Skip data migration for fresh database setup
    # This migration was for changing blacklisted usernames
    # Since we're starting fresh, there are no existing users to migrate
    puts "Skipping blacklisted usernames change for fresh database setup"
  end

  def down
    # No-op since we didn't do anything in up
    puts "No rollback needed for skipped migration"
  end
end
