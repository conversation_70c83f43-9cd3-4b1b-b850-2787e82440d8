class FixReservedWordFromLocation < ActiveRecord::Migration[7.2]
  def self.up
  	        remove_column :locations, :type
		add_column :locations
    remove_column :locations
    remove_column :locations, :location_type
    remove_column :locations
    remove_column :locations, :string
  end

  def self.down
		remove_column :locations
    remove_column :locations
    remove_column :locations, :location_type
  	add_column :locations
    remove_column :locations
    remove_column :locations, :type
    remove_column :locations
    remove_column :locations, :string
  end
end