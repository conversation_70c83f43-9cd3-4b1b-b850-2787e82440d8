class ChangeSettingsToNewFormat < ActiveRecord::Migration[7.2]
  class Setting < ApplicationRecord
  end
  def self.up
    change_column :people, :preferences, :text
    Person.all.each do |person|
      if settings = Setting.find_by_person_id(person.id)
        person.update_attributes({:preferences => {
          "email_about_new_messages" => (settings.email_when_new_message == true),
          "email_about_new_comments_to_own_listing" => (settings.email_when_new_comment == true),
          "email_when_conversation_accepted" => true,
          "email_when_conversation_rejected" => true,
          "email_when_new_friend_request" => (settings.email_when_new_friend_request == true),
          "email_when_new_feedback_on_transaction" => (settings.email_when_new_comment_to_kassi_event == true),
          "email_when_new_listing_from_friend" => (settings.email_when_new_listing_from_friend == true)}})

      end
      print "."; STDOUT.flush
    end
    puts ""
  end

  def self.down
    change_column :people, :preferences, :string
  end
end
