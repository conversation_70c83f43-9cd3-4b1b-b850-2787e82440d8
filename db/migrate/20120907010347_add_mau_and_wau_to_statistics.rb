class AddMauAndWauToStatistics < ActiveRecord::Migration[7.2]
  def self.up
    add_column :statistics, :mau_g1_count, :integer
    add_column :statistics, :wau_g1_count, :integer
    # Skip data migration for fresh database setup
    # This migration was for updating existing statistics
    # Since we're starting fresh, there are no existing records to update
  end

  def self.down
        remove_column :statistics, :mau_g1_count
    remove_column :statistics
    remove_column :statistics, :wau_g1_count
  end
end