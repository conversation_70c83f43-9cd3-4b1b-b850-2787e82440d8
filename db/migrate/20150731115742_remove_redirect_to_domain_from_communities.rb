class RemoveRedirectToDomainFromCommunities < ActiveRecord::Migration[7.2]
  def up
            remove_column :communities, :redirect_to_domain
  end

  def down
    add_column :communities
    remove_column :communities
    remove_column :communities, :redirect_to_domain
    remove_column :communities
    remove_column :communities, :boolean
    remove_column :communities
    remove_column :communities, default: false
    remove_column :communities
    remove_column :communities, null: false
  end
end