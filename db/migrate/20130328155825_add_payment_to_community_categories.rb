class AddPaymentToCommunityCategories < ActiveRecord::Migration[7.2]
  def up
    add_column :community_categories, :payment, :boolean, :default => false
    # Skip data migration for fresh database setup
    # This migration was for updating existing categories with payment placeholders
    # Since we're starting fresh, there are no existing categories to update
    puts "Skipping payment placeholders update for fresh database setup"
  end

  def down
    remove_column :community_categories, :payment
  end
end
