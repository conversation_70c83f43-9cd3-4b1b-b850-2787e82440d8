class RemoveTimestampsFromListingFollowers < ActiveRecord::Migration[7.2]
  def self.up
        remove_column :listing_followers, :created_at
    remove_column :listing_followers
    remove_column :listing_followers, :updated_at
  end

  def self.down
    add_column :listing_followers
    remove_column :listing_followers, :created_at
    remove_column :listing_followers, :datetime
    add_column :listing_followers
    remove_column :listing_followers, :updated_at
    remove_column :listing_followers, :datetime
  end
end