class ChangePaymentRowSumCurrencyToCurrency < ActiveRecord::Migration[7.2]
  def up
    # Skip this migration since CreatePaymentRows now creates the currency column directly
    # This migration was meant to rename sum_currency to currency
    # Since we fixed CreatePaymentRows to create currency directly, this migration is no longer needed
    puts "Skipping currency column rename - already created correctly in CreatePaymentRows"
  end

  def down
    # No-op since we didn't do anything in up
    puts "No rollback needed for skipped migration"
  end
end
