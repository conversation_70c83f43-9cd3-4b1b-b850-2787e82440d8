class ChangeBraintreePreauthorizationProcessesToFree < ActiveRecord::Migration[7.2]
  def up
    # Change all preauthorize processes to none unless they are in use with Paypal gateway
    execute "UPDATE transaction_processes tp
             SET tp.process = 'none'
             WHERE tp.process = 'preauthorize' AND (
               SELECT COUNT(*) = false
               FROM payment_settings ps
               WHERE ps.community_id = tp.community_id AND
                     ps.active = true AND
                     ps.payment_process = 'preauthorize'
             )"
  end

  def down
    execute "UPDATE transaction_processes SET process = old_process WHERE old_process = 'preauthorize'"
  end
end
