class FixLocationAgain < ActiveRecord::Migration[7.2]
  def self.up
  	        remove_column :listings, :destionation_loc_id
	add_column :listings
    remove_column :listings
    remove_column :listings, :destination_loc_id
    remove_column :listings
    remove_column :listings, :integer
  end

  def self.down
  	add_column :listings
    remove_column :listings
    remove_column :listings, :destionation_loc_id
	remove_column :listings
    remove_column :listings
    remove_column :listings, :destination_loc_id
    remove_column :listings
    remove_column :listings, :integer
  end
end