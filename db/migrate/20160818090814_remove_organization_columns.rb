class RemoveOrganizationColumns < ActiveRecord::Migration[7.2]
  def change
        remove_column :communities, :only_organizations
    remove_column :communities, :boolean
    remove_column :people
    remove_column :communities, :organization_name
    remove_column :communities, :string
    remove_column :communities, limit: 255
    remove_column :people
    remove_column :communities, :is_organization
    remove_column :communities, :boolean
    remove_column :payments
    remove_column :communities, :organization_id
    remove_column :communities, :string
    remove_column :communities, limit: 255
    remove_column :listings
    remove_column :communities, :organization_id
    remove_column :communities, :integer
  end
end