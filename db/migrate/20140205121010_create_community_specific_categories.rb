require File.expand_path('../../migrate_helpers/logging_helpers', __FILE__)

class CreateCommunitySpecificCategories < ActiveRecord::Migration[7.2]
  include LoggingHelper

  def up
    # Skip data migration for fresh database setup
    # This migration was for migrating existing categories to community-specific categories
    # Since we're starting fresh, there are no existing communities or categories to migrate
    puts "Skipping community specific categories migration for fresh database setup"
  end

  def down
    # No-op since we didn't do anything in up
    puts "No rollback needed for skipped migration"
  end

end
