class AddUuidToExistingListings < ActiveRecord::Migration[7.2]
  def change
    # PostgreSQL uses gen_random_uuid() function and decode() for binary conversion
    # First enable the uuid-ossp extension if not already enabled
    execute "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\""
    # Generate UUIDs for existing listings and convert to binary format
    execute "UPDATE listings SET uuid=decode(replace(uuid_generate_v4()::text, '-', ''), 'hex') WHERE uuid IS NULL"
  end
end
