class AddStateFieldsToCategories < ActiveRecord::Migration[5.1]
  def up
    add_column :categories, :state_page_title, :string
    add_column :categories, :state_page_meta_description, :text
    add_column :categories, :state_page_header_title, :string
  end

  def down
        remove_column :categories, :state_page_title
    remove_column :categories
    remove_column :categories, :state_page_meta_description
    remove_column :categories
    remove_column :categories, :state_page_header_title
  end
end