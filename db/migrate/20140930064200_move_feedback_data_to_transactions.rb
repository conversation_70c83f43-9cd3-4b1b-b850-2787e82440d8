class MoveFeedbackDataToTransactions < ActiveRecord::Migration[7.2]
  def up
    execute('
      UPDATE transactions
      SET starter_skipped_feedback = participations.feedback_skipped
      FROM participations
      WHERE transactions.conversation_id = participations.conversation_id
        AND participations.is_starter = TRUE
    ')

    execute('
      UPDATE transactions
      SET author_skipped_feedback = participations.feedback_skipped
      FROM participations
      WHERE transactions.conversation_id = participations.conversation_id
        AND participations.is_starter = FALSE
    ')
  end

  def down
    execute('
      UPDATE participations
      SET feedback_skipped = transactions.starter_skipped_feedback
      FROM transactions
      WHERE participations.conversation_id = transactions.conversation_id
        AND transactions.starter_id = participations.person_id
        AND participations.is_starter = TRUE
    ')

    execute('
      UPDATE participations
      SET feedback_skipped = transactions.author_skipped_feedback
      FROM transactions
      WHERE participations.conversation_id = transactions.conversation_id
        AND transactions.starter_id != participations.person_id
        AND participations.is_starter = FALSE
    ')

    execute('
      UPDATE transactions
      SET starter_skipped_feedback = 0,
          author_skipped_feedback = 0
    ')
  end
end
