class SetPriceQuantityPlaceholderNullToCommunitiesThatUseUnits < ActiveRecord::Migration[7.2]
  def up
    execute(<<-SQL)
      UPDATE listing_shapes
      SET price_quantity_placeholder = NULL
      FROM listing_units
      WHERE listing_shapes.id = listing_units.listing_shape_id
        AND listing_shapes.price_quantity_placeholder IS NOT NULL
        AND listing_units.id IS NOT NULL
    SQL
  end

  def down
    # Deleting data, there's no way to get it back.
  end
end
