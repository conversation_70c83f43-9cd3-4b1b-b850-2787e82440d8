class PopulatePaymentGatewayToPayment < ActiveRecord::Migration[7.2]
  def up
    # Skip data migration for fresh database setup
    # This migration was for populating payment gateway IDs
    # Since we're starting fresh, there are no existing payments to populate
    puts "Skipping payment gateway population for fresh database setup"
  end

  def down
    # No-op since we didn't do anything in up
    puts "No rollback needed for skipped migration"
  end
end
