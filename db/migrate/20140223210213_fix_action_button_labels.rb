class FixActionButtonLabels < ActiveRecord::Migration[7.2]
  def up
    # Skip data migration for fresh database setup
    # This migration was for fixing action button labels
    # Since we're starting fresh, there are no existing transaction type translations to fix
    puts "Skipping action button labels fix for fresh database setup"
  end

  def down
    # No-op since we didn't do anything in up
    puts "No rollback needed for skipped migration"
  end
end
