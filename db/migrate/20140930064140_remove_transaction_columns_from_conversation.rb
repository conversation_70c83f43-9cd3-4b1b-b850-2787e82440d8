class RemoveTransactionColumnsFromConversation < ActiveRecord::Migration[7.2]
  def up
            remove_column :conversations, :type
    remove_column :conversations
    remove_column :conversations, :automatic_confirmation_after_days
  end

  def down
    add_column :conversations
    remove_column :conversations
    remove_column :conversations, :type
    remove_column :conversations
    remove_column :conversations, :string
    add_column :conversations
    remove_column :conversations
    remove_column :conversations, :automatic_confirmation_after_days
    remove_column :conversations
    remove_column :conversations, :integer
  end
end