class AddTitleAndMetaToCommunityCustomizations < ActiveRecord::Migration[5.1]
  def up
    add_column :community_customizations, :title, :string
    add_column :community_customizations, :meta, :text

    CommunityCustomization.update_all(title: 'Welcome to Petmasters', meta: 'Explore our online marketplace matching the best pet professionals with pet lovers like you. Discover licensed and trusted service providers in grooming, therapy, photography, training, and more!')
  end

  def down
            remove_column :community_customizations, :meta
    remove_column :community_customizations
    remove_column :community_customizations
    remove_column :community_customizations, :title
  end
end