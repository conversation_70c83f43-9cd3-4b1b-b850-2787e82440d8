class PopulateDistanceUnitInMarketplaceConfigurations < ActiveRecord::Migration[7.2]
  def up
    # Run this population script after all the codes are in production
    execute(<<-SQL)
      UPDATE marketplace_configurations
      SET distance_unit = 'imperial'
      FROM communities
      WHERE marketplace_configurations.community_id = communities.id
        AND communities.country = 'US'
    SQL
  end

  def down
    # These kind of settings should not be changed when rolling back migrations.
    # Admins might have changed them already
  end
end
