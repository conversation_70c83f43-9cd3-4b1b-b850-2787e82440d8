class RemovePaymentsInUseFromCommunities < ActiveRecord::Migration[7.2]
  def up
            remove_column :communities, :payments_in_use
  end

  def down
    add_column :communities
    remove_column :communities
    remove_column :communities, :payments_in_use
    remove_column :communities
    remove_column :communities, :boolean
    remove_column :communities
    remove_column :communities, :default => false
  end
end