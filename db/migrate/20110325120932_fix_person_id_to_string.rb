class FixPersonIdToString < ActiveRecord::Migration[7.2]
  def self.up
  	        remove_column :locations, :person_id
  	add_column :locations
    remove_column :locations
    remove_column :locations, :person_id
    remove_column :locations
    remove_column :locations, :string

  end

  def self.down
  	remove_column :locations
    remove_column :locations
    remove_column :locations, :person_id
  	add_column :locations
    remove_column :locations
    remove_column :locations, :person_id
    remove_column :locations
    remove_column :locations, :integer
  end
end