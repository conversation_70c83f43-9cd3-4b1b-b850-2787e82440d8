class MigrateListingImagesToPaperClip < ActiveRecord::Migration[7.2]
  def self.up
    # Skip image migration for fresh database setup
    # This migration was for copying existing images to paperclip
    # Since we're starting fresh, there are no existing images to migrate
    say "Skipping image migration for fresh database setup"
  end

  def self.down
    raise  ActiveRecord::IrreversibleMigration, "Deletion of the paperclip image files is not implemented.\
       If you wish to rollback, you can quite safely remove this IrreversibleMigration."
  end
end
