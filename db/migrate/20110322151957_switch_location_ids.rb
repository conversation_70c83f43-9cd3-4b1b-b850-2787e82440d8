class SwitchLocationIds < ActiveRecord::Migration[7.2]
  def self.up
      remove_column :people, :location_id
  remove_column :listings
    remove_column :people, :origin_loc_id
  remove_column :listings
    remove_column :people, :destination_loc_id
  add_column :locations
    remove_column :people, :person_id
    remove_column :people, :integer
  add_column :locations
    remove_column :people, :listing_id
    remove_column :people, :integer
  end

  def self.down
  add_column :people
    remove_column :people, :location_id
  add_column :listings
    remove_column :people, :origin_loc_id
  add_column :listings
    remove_column :people, :destination_loc_id
  remove_column :locations
    remove_column :people, :person_id
  remove_column :locations
    remove_column :people, :listing_id
  end
end