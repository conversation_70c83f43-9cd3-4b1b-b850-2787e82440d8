class RemoveUnusedListingIndecies < ActiveRecord::Migration[7.2]
  disable_ddl_transaction!

  def up
    begin
      remove_index "listings", name: "index_listings_on_listing_type"
    rescue ActiveRecord::StatementInvalid
      # Index doesn't exist, ignore
    end
    begin
      remove_index "listings", name: "index_listings_on_share_type_id"
    rescue ActiveRecord::StatementInvalid
      # Index doesn't exist, ignore
    end
  end

  def down
    add_index "listings", ["listing_type_old"], :name => "index_listings_on_listing_type"
    add_index "listings", ["share_type_id"], :name => "index_listings_on_share_type_id"
  end
end
