class RemoveTransactionProposalFromConversations < ActiveRecord::Migration[7.2]
  def self.up
            remove_column :conversations, :transaction_proposal
  end

  def self.down
    add_column :conversations
    remove_column :conversations
    remove_column :conversations, :transaction_proposal
    remove_column :conversations
    remove_column :conversations, :boolean
    remove_column :conversations
    remove_column :conversations, :default => 1
  end
end