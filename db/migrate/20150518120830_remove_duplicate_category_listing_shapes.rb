class RemoveDuplicateCategoryListingShapes < ActiveRecord::Migration[7.2]
  def up
    add_column :category_listing_shapes, :temp_id, :primary_key

    execute(<<-SQL)
      DELETE FROM category_listing_shapes
      WHERE temp_id NOT IN (
        SELECT MIN(temp_id)
        FROM category_listing_shapes
        GROUP BY category_id, listing_shape_id
      )
    SQL

    remove_column :category_listing_shapes, :temp_id
  end

  def down
    # noop
  end
end
