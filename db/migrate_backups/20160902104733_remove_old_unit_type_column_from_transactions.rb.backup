class RemoveOldUnitTypeColumnFromTransactions < ActiveRecord::Migration[7.2]
  def up
    # Remove old_unit_type column if it exists
    remove_column :transactions, :old_unit_type if column_exists?(:transactions, :old_unit_type)
  end

  def down
    # Add back the column if needed for rollback
    add_column :transactions, :old_unit_type, :string, limit: 32 unless column_exists?(:transactions, :old_unit_type)
  end
end