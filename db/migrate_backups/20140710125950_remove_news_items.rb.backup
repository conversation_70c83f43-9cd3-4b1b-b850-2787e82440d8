class RemoveNewsItems < ActiveRecord::Migration[7.2]
  def self.up
    drop_table :news_items
        remove_column :communities, :news_enabled
  end
  
  def self.down
    add_column :communities
    remove_column :communities, :news_enabled
    remove_column :communities, :boolean
    remove_column :communities, :default => true
    create_table :news_items do |t|
      t.string :title
      t.text :content
      t.integer :community_id
      t.string :author_id

      t.timestamps
    end
  end
end