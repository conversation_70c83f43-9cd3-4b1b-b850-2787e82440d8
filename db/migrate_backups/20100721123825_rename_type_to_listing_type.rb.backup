class RenameTypeToListingType < ActiveRecord::Migration[7.2]
  def self.up
        remove_column :listings, :type
    add_column :listings
    remove_column :listings, :listings_type
    remove_column :listings, :string
  end

  def self.down
    remove_column :listings
    remove_column :listings, :listings_type
    add_column :listings
    remove_column :listings, :type
    remove_column :listings, :string
  end
end