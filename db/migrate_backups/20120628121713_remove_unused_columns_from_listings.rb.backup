class RemoveUnusedColumnsFromListings < ActiveRecord::Migration[7.2]
  def self.up
        remove_column :listings, :content
    remove_column :listings
    remove_column :listings, :good_thru
    remove_column :listings
    remove_column :listings, :status
    remove_column :listings
    remove_column :listings, :value_cc
    remove_column :listings
    remove_column :listings, :value_other
  end

  def self.down
    add_column :listings
    remove_column :listings, :content
    remove_column :listings, :text
    add_column :listings
    remove_column :listings, :good_thru
    remove_column :listings, :date
    add_column :listings
    remove_column :listings, :status
    remove_column :listings, :string
    add_column :listings
    remove_column :listings, :value_cc
    remove_column :listings, :integer
    add_column :listings
    remove_column :listings, :value_other
    remove_column :listings, :string    
  end
end