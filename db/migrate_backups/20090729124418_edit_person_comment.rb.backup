class EditPersonComment < ActiveRecord::Migration[7.2]
  def self.up
        remove_column :person_comments, :task_type
    remove_column :person_comments
    remove_column :person_comments, :task_id
    change_column :person_comments
    remove_column :person_comments, :grade
    remove_column :person_comments, :float
  end

  def self.down
    add_column :person_comments
    remove_column :person_comments, :task_type
    remove_column :person_comments, :string
    add_column :person_comments
    remove_column :person_comments, :task_id
    remove_column :person_comments, :integer
    change_column :person_comments
    remove_column :person_comments, :grade
    remove_column :person_comments, :integer
  end
end