class RemoveMangopayFromPeople < ActiveRecord::Migration[7.2]
  def up
        remove_column :people, :mangopay_id
    remove_column :people
    remove_column :people, :mangopay_beneficiary_id
    remove_column :people
    remove_column :people, :bic
    remove_column :people
    remove_column :people, :bank_account_owner_name
    remove_column :people
    remove_column :people, :iban
    remove_column :people
    remove_column :people, :bank_account_owner_address
  end

  def down
    add_column :people
    remove_column :people, :mangopay_id
    remove_column :people, :string
    add_column :people
    remove_column :people, :bic
    remove_column :people, :string
    add_column :people
    remove_column :people, :bank_account_owner_name
    remove_column :people, :string
    add_column :people
    remove_column :people, :iban
    remove_column :people, :string
    add_column :people
    remove_column :people, :bank_account_owner_address
    remove_column :people, :string
    add_column :people
    remove_column :people, :mangopay_beneficiary_id
    remove_column :people, :string
  end
end