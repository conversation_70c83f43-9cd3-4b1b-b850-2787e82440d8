class RemoveReadAndAddIsReadToPersonConversation < ActiveRecord::Migration[7.2]
  def self.up
        remove_column :person_conversations, :read
    add_column :person_conversations
    remove_column :person_conversations, :is_read
    remove_column :person_conversations, :integer
    remove_column :person_conversations, :default => 0
  end

  def self.down
    remove_column :person_conversations
    remove_column :person_conversations, :is_read
    add_column :person_conversations
    remove_column :person_conversations, :read
    remove_column :person_conversations, :boolean
  end
end