class AddStarterAndListingAuthorUuidsToTransactions < ActiveRecord::Migration[7.2]

  def up
    # PostgreSQL uses BYTEA for binary data instead of MySQL's BINARY type
    # PostgreSQL doesn't support AFTER clause, so columns will be added at the end
    add_column :transactions, :starter_uuid, :binary, limit: 16
    add_column :transactions, :listing_author_uuid, :binary, limit: 16
    # NOT NULL and UNIQUE constraints are coming in separate
    # migrations once the old data is migrated
  end

  def down
    remove_column :transactions, :starter_uuid
    remove_column :transactions, :listing_author_uuid
  end
end