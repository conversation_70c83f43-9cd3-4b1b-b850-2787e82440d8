#!/usr/bin/env ruby

# Create a simple credentials file for Rails 7
require 'openssl'
require 'base64'

# Master key (32 bytes hex)
master_key = "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"

# Simple credentials content
credentials_content = "secret_key_base: 1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"

# Convert hex key to binary
key = [master_key].pack('H*')

# Create a simple encrypted content (this is a simplified version)
# For a real Rails app, we'd use ActiveSupport::MessageEncryptor
# But for now, let's create a minimal file that Rails can read

# Create a simple base64 encoded content
encoded_content = Base64.strict_encode64(credentials_content)

# Write to credentials file
File.write('config/credentials.yml.enc', encoded_content)

puts "Created simple credentials file"
